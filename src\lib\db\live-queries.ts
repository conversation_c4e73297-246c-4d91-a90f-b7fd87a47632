/**
 * TanStack DB Live Query Helpers - 2025 Implementation
 *
 * Pre-configured live queries with cross-collection joins for common use cases
 * Provides sub-millisecond reactivity with normalized data structures
 */

import { useLiveQuery } from '@tanstack/react-db';
import {
  userCollection,
  ticketCollection,
  replyCollection,
  attachmentCollection,
} from './collections-setup';

// Live query for tickets with creator and assignee information
export const useTicketsWithUsers = (
  tenantId: string,
  filters?: {
    status?: string[];
    priority?: string[];
    assignedTo?: string;
    createdBy?: string;
  }
) => {
  return useLiveQuery((query) => {
    let ticketQuery = query
      .from({ tickets: ticketCollection })
      .join({
        type: 'left',
        from: { creator: userCollection },
        on: ['@tickets.created_by', '=', '@creator.id'],
      })
      .join({
        type: 'left',
        from: { assignee: userCollection },
        on: ['@tickets.assigned_to', '=', '@assignee.id'],
      })
      .where('@tickets.tenant_id', '=', tenantId);

    // Apply filters
    if (filters?.status?.length) {
      ticketQuery = ticketQuery.where('@tickets.status', 'in', filters.status);
    }

    if (filters?.priority?.length) {
      ticketQuery = ticketQuery.where(
        '@tickets.priority',
        'in',
        filters.priority
      );
    }

    if (filters?.assignedTo) {
      ticketQuery = ticketQuery.where(
        '@tickets.assigned_to',
        '=',
        filters.assignedTo
      );
    }

    if (filters?.createdBy) {
      ticketQuery = ticketQuery.where(
        '@tickets.created_by',
        '=',
        filters.createdBy
      );
    }

    return ticketQuery
      .orderBy({ '@tickets.created_at': 'desc' })
      .select(
        '@tickets.*',
        '@creator.first_name as creator_first_name',
        '@creator.last_name as creator_last_name',
        '@creator.email as creator_email',
        '@creator.avatar_url as creator_avatar_url',
        '@assignee.first_name as assignee_first_name',
        '@assignee.last_name as assignee_last_name',
        '@assignee.email as assignee_email',
        '@assignee.avatar_url as assignee_avatar_url'
      );
  });
};

// Live query for a single ticket with all related data
export const useTicketWithDetails = (tenantId: string, ticketId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ tickets: ticketCollection })
      .join({
        type: 'left',
        from: { creator: userCollection },
        on: ['@tickets.created_by', '=', '@creator.id'],
      })
      .join({
        type: 'left',
        from: { assignee: userCollection },
        on: ['@tickets.assigned_to', '=', '@assignee.id'],
      })
      .where('@tickets.tenant_id', '=', tenantId)
      .where('@tickets.id', '=', ticketId)
      .select(
        '@tickets.*',
        '@creator.first_name as creator_first_name',
        '@creator.last_name as creator_last_name',
        '@creator.email as creator_email',
        '@creator.avatar_url as creator_avatar_url',
        '@assignee.first_name as assignee_first_name',
        '@assignee.last_name as assignee_last_name',
        '@assignee.email as assignee_email',
        '@assignee.avatar_url as assignee_avatar_url'
      )
  );
};

// Live query for replies with author information
export const useRepliesWithAuthors = (tenantId: string, ticketId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ replies: replyCollection })
      .join({
        type: 'inner',
        from: { author: userCollection },
        on: ['@replies.author_id', '=', '@author.id'],
      })
      .where('@replies.tenant_id', '=', tenantId)
      .where('@replies.ticket_id', '=', ticketId)
      .orderBy({ '@replies.created_at': 'asc' })
      .select(
        '@replies.*',
        '@author.first_name as author_first_name',
        '@author.last_name as author_last_name',
        '@author.email as author_email',
        '@author.avatar_url as author_avatar_url',
        '@author.role as author_role'
      )
  );
};

// Live query for user's assigned tickets
export const useUserAssignedTickets = (tenantId: string, userId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ tickets: ticketCollection })
      .join({
        type: 'left',
        from: { creator: userCollection },
        on: ['@tickets.created_by', '=', '@creator.id'],
      })
      .where('@tickets.tenant_id', '=', tenantId)
      .where('@tickets.assigned_to', '=', userId)
      .where('@tickets.status', 'not in', ['closed', 'resolved'])
      .orderBy({ '@tickets.updated_at': 'desc' })
      .select(
        '@tickets.*',
        '@creator.first_name as creator_first_name',
        '@creator.last_name as creator_last_name',
        '@creator.email as creator_email'
      )
  );
};

// Live query for recent activity (tickets and replies)
export const useRecentActivity = (tenantId: string, limit = 10) => {
  return useLiveQuery((query) =>
    query
      .from({ tickets: ticketCollection })
      .join({
        type: 'left',
        from: { replies: replyCollection },
        on: ['@tickets.id', '=', '@replies.ticket_id'],
      })
      .join({
        type: 'left',
        from: { user: userCollection },
        on: ['@replies.author_id', '=', '@user.id'],
      })
      .where('@tickets.tenant_id', '=', tenantId)
      .orderBy({ '@tickets.updated_at': 'desc', '@replies.created_at': 'desc' })
      .limit(limit)
      .select(
        '@tickets.id as ticket_id',
        '@tickets.title',
        '@tickets.status',
        '@tickets.updated_at as ticket_updated_at',
        '@replies.id as reply_id',
        '@replies.content',
        '@replies.created_at as reply_created_at',
        '@user.first_name',
        '@user.last_name',
        '@user.avatar_url'
      )
  );
};

// Live query for ticket statistics by status
export const useTicketStats = (tenantId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ tickets: ticketCollection })
      .where('@tickets.tenant_id', '=', tenantId)
      .groupBy('@tickets.status')
      .select('@tickets.status', 'COUNT(@tickets.id) as count')
  );
};

// Live query for user workload (assigned tickets by status)
export const useUserWorkload = (tenantId: string, userId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ tickets: ticketCollection })
      .where('@tickets.tenant_id', '=', tenantId)
      .where('@tickets.assigned_to', '=', userId)
      .groupBy('@tickets.status', '@tickets.priority')
      .select(
        '@tickets.status',
        '@tickets.priority',
        'COUNT(@tickets.id) as count'
      )
  );
};

// Live query for attachments by ticket
export const useTicketAttachments = (tenantId: string, ticketId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ attachments: attachmentCollection })
      .join({
        type: 'left',
        from: { uploader: userCollection },
        on: ['@attachments.uploaded_by', '=', '@uploader.id'],
      })
      .where('@attachments.tenant_id', '=', tenantId)
      .where('@attachments.ticket_id', '=', ticketId)
      .orderBy({ '@attachments.created_at': 'desc' })
      .select(
        '@attachments.*',
        '@uploader.first_name as uploader_first_name',
        '@uploader.last_name as uploader_last_name'
      )
  );
};

// Live query for expanded messages (for caching UI state)
export const useExpandedMessages = (tenantId: string, ticketId: string) => {
  return useLiveQuery((query) =>
    query
      .from({ replies: replyCollection })
      .where('@replies.tenant_id', '=', tenantId)
      .where('@replies.ticket_id', '=', ticketId)
      .where('@replies.is_expanded', '=', true)
      .select('@replies.id', '@replies.is_expanded')
  );
};

// Helper for filtering tickets by multiple criteria
export const useFilteredTickets = (
  tenantId: string,
  filters: {
    search?: string;
    status?: string[];
    priority?: string[];
    assignedTo?: string;
    department?: string;
    dateRange?: { start: string; end: string };
  }
) => {
  return useLiveQuery((query) => {
    let ticketQuery = query
      .from({ tickets: ticketCollection })
      .join({
        type: 'left',
        from: { creator: userCollection },
        on: ['@tickets.created_by', '=', '@creator.id'],
      })
      .join({
        type: 'left',
        from: { assignee: userCollection },
        on: ['@tickets.assigned_to', '=', '@assignee.id'],
      })
      .where('@tickets.tenant_id', '=', tenantId);

    // Apply search filter
    if (filters.search) {
      ticketQuery = ticketQuery.where(
        '@tickets.title',
        'like',
        `%${filters.search}%`
      );
    }

    // Apply status filter
    if (filters.status?.length) {
      ticketQuery = ticketQuery.where('@tickets.status', 'in', filters.status);
    }

    // Apply priority filter
    if (filters.priority?.length) {
      ticketQuery = ticketQuery.where(
        '@tickets.priority',
        'in',
        filters.priority
      );
    }

    // Apply assignee filter
    if (filters.assignedTo) {
      ticketQuery = ticketQuery.where(
        '@tickets.assigned_to',
        '=',
        filters.assignedTo
      );
    }

    // Apply department filter
    if (filters.department) {
      ticketQuery = ticketQuery.where(
        '@tickets.department',
        '=',
        filters.department
      );
    }

    // Apply date range filter
    if (filters.dateRange) {
      ticketQuery = ticketQuery
        .where('@tickets.created_at', '>=', filters.dateRange.start)
        .where('@tickets.created_at', '<=', filters.dateRange.end);
    }

    return ticketQuery
      .orderBy({ '@tickets.created_at': 'desc' })
      .select(
        '@tickets.*',
        '@creator.first_name as creator_first_name',
        '@creator.last_name as creator_last_name',
        '@assignee.first_name as assignee_first_name',
        '@assignee.last_name as assignee_last_name'
      );
  });
};
