/**
 * TanStack DB Collection Setup - 2025 Implementation
 *
 * Creates and configures TanStack DB collections with QueryCollection pattern
 * Integrates with existing TanStack Query infrastructure
 */

import { createCollection } from '@tanstack/react-db';
import { queryCollectionOptions } from '@tanstack/db-collections';
import {
  COLLECTION_CONFIGS,
  UserCollection,
  TicketCollection,
  ReplyCollection,
  AttachmentCollection,
  COLLECTION_IDS,
} from './collections';
import { api } from '@/lib/query-options';

// User Collection - for caching user profiles and avatars
export const userCollection = createCollection<UserCollection>(
  queryCollectionOptions({
    id: COLLECTION_IDS.USERS,
    queryKey: ['users'],
    queryFn: async () => {
      // This will be populated by TanStack Query when user data is fetched
      return [];
    },
    getKey: COLLECTION_CONFIGS.users.getKey,
    schema: COLLECTION_CONFIGS.users.schema,

    // Optimistic mutations for user updates
    onUpdate: async ({ transaction }) => {
      const { original, modified } = transaction.mutations[0];

      // Update user profile via API
      await api.users.updateUser(original.id, {
        first_name: modified.first_name,
        last_name: modified.last_name,
        avatar_url: modified.avatar_url,
      });

      console.log('🔄 User updated optimistically:', modified.id);
    },
  })
);

// Ticket Collection - core ticket data with tenant isolation
export const ticketCollection = createCollection<TicketCollection>(
  queryCollectionOptions({
    id: COLLECTION_IDS.TICKETS,
    queryKey: ['tickets'],
    queryFn: async () => {
      // This will be populated by TanStack Query when tickets are fetched
      return [];
    },
    getKey: COLLECTION_CONFIGS.tickets.getKey,
    schema: COLLECTION_CONFIGS.tickets.schema,

    // Optimistic mutations for ticket operations
    onInsert: async ({ transaction }) => {
      const { modified: newTicket } = transaction.mutations[0];

      // Create ticket via API
      const response = await api.tickets.createTicket({
        title: newTicket.title,
        description: newTicket.description,
        priority: newTicket.priority,
        department: newTicket.department,
        tenant_id: newTicket.tenant_id,
      });

      console.log('🎫 Ticket created optimistically:', response.id);
      return response;
    },

    onUpdate: async ({ transaction }) => {
      const { original, modified } = transaction.mutations[0];

      // Update ticket via API
      await api.tickets.updateTicket(original.id, {
        title: modified.title,
        description: modified.description,
        status: modified.status,
        priority: modified.priority,
        assigned_to: modified.assigned_to,
        tags: modified.tags,
      });

      console.log('🔄 Ticket updated optimistically:', modified.id);
    },
  })
);

// Reply Collection - normalized message/reply data
export const replyCollection = createCollection<ReplyCollection>(
  queryCollectionOptions({
    id: COLLECTION_IDS.REPLIES,
    queryKey: ['replies'],
    queryFn: async () => {
      // This will be populated by TanStack Query when replies are fetched
      return [];
    },
    getKey: COLLECTION_CONFIGS.replies.getKey,
    schema: COLLECTION_CONFIGS.replies.schema,

    // Optimistic mutations for reply operations
    onInsert: async ({ transaction }) => {
      const { modified: newReply } = transaction.mutations[0];

      // Create reply via API
      const response = await api.tickets.createReply(newReply.ticket_id, {
        content: newReply.content,
        is_internal: newReply.is_internal,
        attachments: newReply.attachments,
      });

      // Update ticket reply count optimistically
      ticketCollection.update(newReply.ticket_id, (draft) => {
        draft.reply_count = (draft.reply_count || 0) + 1;
        draft.updated_at = new Date().toISOString();
      });

      console.log('💬 Reply created optimistically:', response.id);
      return response;
    },

    onUpdate: async ({ transaction }) => {
      const { original, modified } = transaction.mutations[0];

      // Update reply via API
      await api.tickets.updateReply(original.id, {
        content: modified.content,
        is_internal: modified.is_internal,
      });

      console.log('🔄 Reply updated optimistically:', modified.id);
    },

    onDelete: async ({ transaction }) => {
      const { original } = transaction.mutations[0];

      // Delete reply via API
      await api.tickets.deleteReply(original.id);

      // Update ticket reply count optimistically
      ticketCollection.update(original.ticket_id, (draft) => {
        draft.reply_count = Math.max((draft.reply_count || 1) - 1, 0);
        draft.updated_at = new Date().toISOString();
      });

      console.log('🗑️ Reply deleted optimistically:', original.id);
    },
  })
);

// Attachment Collection - file attachments with metadata
export const attachmentCollection = createCollection<AttachmentCollection>(
  queryCollectionOptions({
    id: COLLECTION_IDS.ATTACHMENTS,
    queryKey: ['attachments'],
    queryFn: async () => {
      // This will be populated by TanStack Query when attachments are fetched
      return [];
    },
    getKey: COLLECTION_CONFIGS.attachments.getKey,
    schema: COLLECTION_CONFIGS.attachments.schema,

    // Optimistic mutations for attachment operations
    onInsert: async ({ transaction }) => {
      const { modified: newAttachment } = transaction.mutations[0];

      // Upload attachment via API
      const response = await api.attachments.uploadAttachment({
        ticket_id: newAttachment.ticket_id,
        reply_id: newAttachment.reply_id,
        name: newAttachment.name,
        size: newAttachment.size,
        type: newAttachment.type,
      });

      console.log('📎 Attachment uploaded optimistically:', response.id);
      return response;
    },

    onDelete: async ({ transaction }) => {
      const { original } = transaction.mutations[0];

      // Delete attachment via API
      await api.attachments.deleteAttachment(original.id);

      console.log('🗑️ Attachment deleted optimistically:', original.id);
    },
  })
);

// Collection registry for easy access
export const collections = {
  users: userCollection,
  tickets: ticketCollection,
  replies: replyCollection,
  attachments: attachmentCollection,
} as const;

// Helper functions for common operations
export const collectionHelpers = {
  // Get all collections for a tenant
  getTenantCollections: (tenantId: string) => ({
    users: userCollection,
    tickets: ticketCollection,
    replies: replyCollection,
    attachments: attachmentCollection,
  }),

  // Clear all collections for a tenant (for logout/tenant switch)
  clearTenantData: async (tenantId: string) => {
    // Note: TanStack DB will handle this through query invalidation
    console.log('🧹 Clearing tenant data for:', tenantId);
  },

  // Hydrate collections from TanStack Query data
  hydrateFromQuery: {
    users: (users: any[]) => {
      users.forEach((user) => {
        userCollection.insert({
          ...user,
          cached_at: Date.now(),
        });
      });
    },

    tickets: (tickets: any[]) => {
      tickets.forEach((ticket) => {
        ticketCollection.insert({
          ...ticket,
          cached_at: Date.now(),
        });
      });
    },

    replies: (replies: any[]) => {
      replies.forEach((reply) => {
        replyCollection.insert({
          ...reply,
          cached_at: Date.now(),
        });
      });
    },

    attachments: (attachments: any[]) => {
      attachments.forEach((attachment) => {
        attachmentCollection.insert({
          ...attachment,
          cached_at: Date.now(),
        });
      });
    },
  },
};

// Export collection instances
export {
  userCollection,
  ticketCollection,
  replyCollection,
  attachmentCollection,
};
