/**
 * TanStack DB Collection Schemas - 2025 Implementation
 *
 * Normalized collections for tickets, replies, and users with cross-collection joins
 * Replaces Dexie.js with TanStack DB's reactive live queries and optimistic mutations
 */

import { z } from 'zod';

// Base schema for all collections with tenant isolation
const BaseCollectionSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
});

// User collection schema - normalized user data
export const UserCollectionSchema = BaseCollectionSchema.extend({
  clerk_id: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string(),
  role: z.enum(['admin', 'agent', 'user']),
  avatar_url: z.string().optional(),
  status: z.enum(['active', 'inactive']).default('active'),
  // Metadata for caching and performance
  last_seen: z.string().optional(),
  cached_at: z.number(),
});

// Ticket collection schema - core ticket data
export const TicketCollectionSchema = BaseCollectionSchema.extend({
  title: z.string(),
  description: z.string(),
  status: z.enum(['new', 'open', 'in_progress', 'resolved', 'closed']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  department: z.string(),

  // User relationships (foreign keys for joins)
  created_by: z.string(), // User ID
  assigned_to: z.string().optional(), // User ID
  assigned_by: z.string().optional(), // User ID
  opened_by: z.string().optional(), // User ID

  // Timestamps for workflow tracking
  assigned_at: z.string().optional(),
  opened_at: z.string().optional(),
  resolved_at: z.string().optional(),
  closed_at: z.string().optional(),
  due_date: z.string().optional(),

  // Additional metadata
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.string(), z.any()).default({}),

  // Caching metadata
  cached_at: z.number(),
  reply_count: z.number().default(0), // Denormalized for performance
});

// Reply/Message collection schema - normalized message data
export const ReplyCollectionSchema = BaseCollectionSchema.extend({
  ticket_id: z.string(), // Foreign key to ticket
  content: z.string(),

  // Author relationship
  author_id: z.string(), // User ID for join

  // Message metadata
  is_internal: z.boolean().default(false),
  is_system: z.boolean().default(false),

  // Attachments as embedded documents
  attachments: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        size: z.number(),
        type: z.string(),
        url: z.string().optional(),
      })
    )
    .default([]),

  // Caching and UI state
  cached_at: z.number(),
  is_expanded: z.boolean().default(false), // UI state for message expansion
});

// Attachment collection schema - separate for better normalization
export const AttachmentCollectionSchema = BaseCollectionSchema.extend({
  ticket_id: z.string(),
  reply_id: z.string().optional(),
  name: z.string(),
  size: z.number(),
  type: z.string(),
  url: z.string().optional(),
  uploaded_by: z.string(), // User ID
  cached_at: z.number(),
});

// TypeScript types derived from schemas
export type UserCollection = z.infer<typeof UserCollectionSchema>;
export type TicketCollection = z.infer<typeof TicketCollectionSchema>;
export type ReplyCollection = z.infer<typeof ReplyCollectionSchema>;
export type AttachmentCollection = z.infer<typeof AttachmentCollectionSchema>;

// Collection configuration for TanStack DB
export interface CollectionConfig {
  id: string;
  schema: z.ZodSchema;
  getKey: (item: any) => string;
  indexes?: string[];
}

// Collection configurations
export const COLLECTION_CONFIGS = {
  users: {
    id: 'users',
    schema: UserCollectionSchema,
    getKey: (item: UserCollection) => item.id,
    indexes: ['tenant_id', 'clerk_id', 'email', '[tenant_id+role]'],
  } as CollectionConfig,

  tickets: {
    id: 'tickets',
    schema: TicketCollectionSchema,
    getKey: (item: TicketCollection) => item.id,
    indexes: [
      'tenant_id',
      'status',
      'priority',
      'created_by',
      'assigned_to',
      '[tenant_id+status]',
      '[tenant_id+created_by]',
      '[tenant_id+assigned_to]',
      'created_at',
    ],
  } as CollectionConfig,

  replies: {
    id: 'replies',
    schema: ReplyCollectionSchema,
    getKey: (item: ReplyCollection) => item.id,
    indexes: [
      'tenant_id',
      'ticket_id',
      'author_id',
      '[tenant_id+ticket_id]',
      '[ticket_id+created_at]',
      'created_at',
    ],
  } as CollectionConfig,

  attachments: {
    id: 'attachments',
    schema: AttachmentCollectionSchema,
    getKey: (item: AttachmentCollection) => item.id,
    indexes: [
      'tenant_id',
      'ticket_id',
      'reply_id',
      'uploaded_by',
      '[tenant_id+ticket_id]',
    ],
  } as CollectionConfig,
} as const;

// Query builder helpers for common join patterns
export const QUERY_PATTERNS = {
  // Ticket with creator and assignee
  ticketWithUsers: {
    from: 'tickets',
    joins: [
      {
        type: 'left' as const,
        from: 'users',
        alias: 'creator',
        on: ['@tickets.created_by', '=', '@creator.id'],
      },
      {
        type: 'left' as const,
        from: 'users',
        alias: 'assignee',
        on: ['@tickets.assigned_to', '=', '@assignee.id'],
      },
    ],
  },

  // Replies with author information
  repliesWithAuthor: {
    from: 'replies',
    joins: [
      {
        type: 'inner' as const,
        from: 'users',
        alias: 'author',
        on: ['@replies.author_id', '=', '@author.id'],
      },
    ],
  },

  // Ticket with replies and authors
  ticketWithRepliesAndAuthors: {
    from: 'tickets',
    joins: [
      {
        type: 'left' as const,
        from: 'replies',
        on: ['@tickets.id', '=', '@replies.ticket_id'],
      },
      {
        type: 'left' as const,
        from: 'users',
        alias: 'reply_author',
        on: ['@replies.author_id', '=', '@reply_author.id'],
      },
    ],
  },
} as const;

// Export collection IDs for type safety
export const COLLECTION_IDS = {
  USERS: 'users',
  TICKETS: 'tickets',
  REPLIES: 'replies',
  ATTACHMENTS: 'attachments',
} as const;
